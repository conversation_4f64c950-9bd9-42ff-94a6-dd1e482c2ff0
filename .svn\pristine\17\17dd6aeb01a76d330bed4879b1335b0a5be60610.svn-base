"""
LLM模型配置模块（简化版）
基于您原始方法的简洁性，支持每个模型独立配置不同的厂商提供商
"""
from langchain.chat_models import init_chat_model
import os
from utils.log_utils import get_logger

logger = get_logger(__name__)

def get_langchain_provider(provider_name):
    """
    将配置中的提供商名称映射到LangChain的model_provider
    
    Args:
        provider_name (str): 配置中的提供商名称
        
    Returns:
        str: LangChain的model_provider名称
    """
    provider_mapping = {
        "deepseek": "deepseek",
        "tongyi": "openai",      # 通义千问通过OpenAI兼容接口
        "siliconflow": "openai", # 硅基流动通过OpenAI兼容接口
        "openai": "openai"
    }
    return provider_mapping.get(provider_name, "openai")

# 主要Agent模型配置
AGENT_MODEL_NAME = os.getenv("AGENT_MODEL_NAME", "deepseek-chat")
AGENT_MODEL_PROVIDER = os.getenv("AGENT_MODEL_PROVIDER", "deepseek")
AGENT_MODEL_TEMPERATURE = float(os.getenv("AGENT_MODEL_TEMPERATURE", "0.01"))

# 意图识别模型配置  
INTENT_MODEL_NAME = os.getenv("INTENT_MODEL_NAME", "deepseek-chat")
INTENT_MODEL_PROVIDER = os.getenv("INTENT_MODEL_PROVIDER", "deepseek")
INTENT_MODEL_TEMPERATURE = float(os.getenv("INTENT_MODEL_TEMPERATURE", "0"))

# 标题生成模型配置
TITLE_MODEL_NAME = os.getenv("TITLE_MODEL_NAME", "qwen-turbo-latest")
TITLE_MODEL_PROVIDER = os.getenv("TITLE_MODEL_PROVIDER", "tongyi")
TITLE_MODEL_TEMPERATURE = float(os.getenv("TITLE_MODEL_TEMPERATURE", "0.2"))

# 创建模型实例 - 使用您原始方法的简洁逻辑
logger.info(f"创建Agent模型: {AGENT_MODEL_NAME} (提供商: {AGENT_MODEL_PROVIDER})")
llm = init_chat_model(
    model=AGENT_MODEL_NAME,
    model_provider=get_langchain_provider(AGENT_MODEL_PROVIDER),
    tags=["wangwei"],
    temperature=AGENT_MODEL_TEMPERATURE
)

logger.info(f"创建意图模型: {INTENT_MODEL_NAME} (提供商: {INTENT_MODEL_PROVIDER})")
intent_llm = init_chat_model(
    model=INTENT_MODEL_NAME,
    model_provider=get_langchain_provider(INTENT_MODEL_PROVIDER),
    tags=["intent"],
    temperature=INTENT_MODEL_TEMPERATURE
)

logger.info(f"创建标题模型: {TITLE_MODEL_NAME} (提供商: {TITLE_MODEL_PROVIDER})")
title_llm = init_chat_model(
    model=TITLE_MODEL_NAME,
    model_provider=get_langchain_provider(TITLE_MODEL_PROVIDER),
    tags=["title"],
    temperature=TITLE_MODEL_TEMPERATURE
)
