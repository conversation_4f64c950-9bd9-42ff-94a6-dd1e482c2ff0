"""
状态管理模块
定义Agent的状态类和状态管理函数
"""
from typing import Optional
from langgraph.prebuilt.chat_agent_executor import AgentState
from langchain_core.messages import AIMessage
from pydantic import BaseModel, Field
from typing import Literal


class State(AgentState):
    """
    扩展基础的AgentState，添加标题管理
    """
    title: Optional[str]  # 动态生成的标题
    title_generated: Optional[bool]  # 防止重复生成

    intent: Optional[str]  # 意图分析结果
    intent_confidence: Optional[float]  # 意图置信度

class IntentModel(BaseModel):
    """
    意图分析模型
    """
    intent: Literal["resource_related", "hardware_related", "bar_info", "user_info", "daily_chat", "other"] = Field(..., description="""意图分类，必须从以下选项中选择一个：
- 'resource_related': 资源相关。用户的意图与游戏或应用资源的管理直接相关。核心场景: 下载、安装、更新、查询游戏/应用，或者查询哪些网吧安装了/没有安装某个游戏/应用。关键词: "游戏", "资源", "下载", "更新", "安装", "有没有", "有哪些", "状态", 以及任何具体的游戏或应用名称。示例: "下载英雄联盟", "哪些网吧有无畏契约？", "10001是什么" (当10001是资源ID时)。
- 'hardware_related': 硬件相关，硬件信息也叫资产信息。用户的意图与查询网吧的硬件配置信息或硬件统计数据相关。核心场景: 查询CPU、内存、显卡、主板等信息，或者对这些硬件进行统计。关键词: "硬件","资产", "配置", "CPU", "内存", "显卡", "主板", "统计"。示例: "查询所有网吧的CPU型号", "统计一下显卡是RTX 4090的电脑数量"。
- 'bar_info': 网吧信息。用户的意图是查询和他账户关联的网吧本身的信息。核心场景: 查询自己管理哪些网吧，或者查询某个具体网吧的详情。关键词: "网吧", "网咖", "列表", "信息"。示例: "我管理着哪些网吧？", "12345是哪个网吧？" (当12345是网吧ID时)。
- 'user_info': 用户信息。用户的意图与查询他自己的身份或账户信息相关。核心场景: 询问 "我是谁"。关键词: "我", "用户", "账户"。示例: "我是谁？"。
- 'daily_chat': 日常聊天。用户的输入是闲聊、打招呼、表达感谢或询问AI自身的信息。核心场景: 与工作无关的互动。关键词: "你好", "谢谢", "再见", "你是谁"。示例: "你好啊"。
- 'other': 其他。无法明确归类到以上任何一种的意图。核心场景: 用户的输入非常模糊、不完整，或者与所有已知功能都无关。示例: "asdasdasd", "今天天气怎么样？"。""")
    intent_confidence: float = Field(..., description="意图置信度, 0-100")
