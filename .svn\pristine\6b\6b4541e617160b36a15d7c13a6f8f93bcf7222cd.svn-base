"""
工具处理策略模块
定义不同工具的中断和人工干预处理策略
"""
from typing import Dict, Literal, Protocol
from langgraph.types import interrupt, Command

# 定义工具处理策略接口
class ToolInterruptStrategy(Protocol):
    """
    工具中断处理策略接口
    定义了处理工具调用中断的通用接口
    """
    async def handle_interrupt(self, tool_call: Dict) -> Command[Literal["model", "tools", "__end__"]]:
        ...

# 下载资源工具的处理策略
class ResourceDownloadStrategy:
    """
    资源下载工具的中断处理策略
    处理initiate_resource_download_or_update工具的人工确认
    """
    async def handle_interrupt(self, tool_call: Dict) -> Command[Literal["model", "tools", "__end__"]]:
        human_review = interrupt(
            {
                "question": "确定进行下载吗?",
                "tool_call": tool_call,
            }
        )
        review_action = human_review["action"]

        if review_action == "continue":
            return Command(goto="tools")
        
        elif review_action == "reject":
            tool_message = {
                "role": "tool",
                "content": "下载操作已被用户取消。请友好地告知用户下载已取消，不要重新发起下载。",
                "name": tool_call["name"],
                "artifact": None,
                "tool_call_id": tool_call["id"],
            }
            return Command(
                goto="model",
                update={"messages": [tool_message]}
            )


# 默认的工具处理策略
class DefaultToolStrategy:
    """默认的工具处理策略，不执行任何中断，直接执行工具调用"""
    async def handle_interrupt(self, tool_call: Dict) -> Command[Literal["model", "tools", "__end__"]]:
        return Command(goto="tools")

class ToolStrategyFactory:
    """
    工具策略工厂
    负责根据工具名称返回对应的处理策略
    """
    def __init__(self):
        """初始化策略映射"""
        self.strategies = {
            "initiate_resource_download_or_update": ResourceDownloadStrategy(),
            # 添加更多工具和对应的策略...
            "default": DefaultToolStrategy()
        }
    
    def get_strategy(self, tool_name: str) -> ToolInterruptStrategy:
        """根据工具名称获取对应的处理策略"""
        return self.strategies.get(tool_name, self.strategies["default"]) 