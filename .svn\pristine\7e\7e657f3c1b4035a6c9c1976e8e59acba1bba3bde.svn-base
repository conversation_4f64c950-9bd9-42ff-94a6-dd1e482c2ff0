"""
代理核心逻辑模块
包含模型调用和工具处理逻辑
"""
from config.llm_config import llm
from core.agent.prompt_templates import prompt_template
from core.agent.state import State, IntentModel
from core.agent.strategies import ToolStrategyFactory
from core.agent.context_engineering import detect_scenario, get_scenario_specific_prompt, get_dynamic_context
from langgraph.types import Command, Literal
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage, AIMessageChunk
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from tools import all_tools, resource_related_tools, hardware_related_tools, common_tools, time_tools
from config.llm_config import title_llm
from core.agent.prompt_templates import title_prompt_template, intent_prompt_template
from config.llm_config import intent_llm
import asyncio
from utils.log_utils import get_logger

logger = get_logger(__name__)

# 创建工具策略工厂实例
strategy_factory = ToolStrategyFactory()

# 存储每个thread_id的停止事件
stop_events = {}

resource_related_keywords = ["下载", "更新", "游戏", "资源"]
hardware_related_keywords = ["硬件", "统计", "cpu", "gpu", "内存", "主板", "显卡"]

async def intent_analysis(state: State, config: RunnableConfig):
    """
    意图分析节点
    
    Args:
        state: 当前对话状态
        
    Returns:
        str: 意图分析结果
    """
    # 获取thread_id
    thread_id = config.get("configurable", {}).get("thread_id")

    # 检查是否有停止事件
    if thread_id and thread_id in stop_events and stop_events[thread_id].is_set():
        logger.info(f"意图分析节点检测到中断事件，thread_id: {thread_id}")
        return {
            **state,
            "intent": "other",
            "intent_confidence": 0,
        }

    # 简单意图分析
    simple_intent = simple_intent_analysis(state)
    if simple_intent != "other":
        return {
            **state,
            "intent": simple_intent,
            "intent_confidence": 100,
        }

    try:
        chain = intent_prompt_template | intent_llm.with_structured_output(IntentModel)
        conversation_messages = [
            message 
            for message in state["messages"]
            if message.type in ("human")
            or (message.type == "ai" and not message.tool_calls)
        ]
        result = await chain.ainvoke({"messages": conversation_messages[-8:]})
        
        # 再次检查是否有停止事件
        if thread_id and thread_id in stop_events and stop_events[thread_id].is_set():
            logger.info(f"意图分析节点完成后检测到中断事件，thread_id: {thread_id}")
            return {
                **state,
                "intent": "other",
                "intent_confidence": 0,
            }
            
        return {
            **state,
            "intent": "other" if result is None else result.intent,
            "intent_confidence": 0 if result is None else result.intent_confidence,
        }
    except Exception as e:
        logger.error(f"意图分析时出错: {e}", exc_info=True)
        return {
            **state,
            "intent": "other",
            "intent_confidence": 0,
        }

def tool_selection(state: State):
    """
    根据意图选择工具
    """
    if state["intent"] == "resource_related" and state["intent_confidence"] >= 80:
        tools_to_use = resource_related_tools
    elif state["intent"] == "hardware_related" and state["intent_confidence"] >= 80:
        tools_to_use = hardware_related_tools
    elif state["intent"] == "bar_info" and state["intent_confidence"] >= 80:
        tools_to_use = common_tools
    elif state["intent"] == "user_info" and state["intent_confidence"] >= 80:
        tools_to_use = common_tools
    elif state["intent"] == "daily_chat" and state["intent_confidence"] >= 80:
        tools_to_use = time_tools
    else:
        tools_to_use = all_tools
    return tools_to_use

async def model(state: State, config: RunnableConfig):
    """
    处理模型调用节点
    
    Args:
        state: 当前对话状态
        
    Returns:
        dict: 更新后的状态
    """
    # 获取thread_id
    thread_id = config.get("configurable", {}).get("thread_id")
    
    # 根据意图选择工具
    tools_to_use = tool_selection(state)

    # 获取用户最新输入
    user_input = ""
    if state["messages"]:
        last_message = state["messages"][-1]
        if hasattr(last_message, 'content'):
            user_input = last_message.content

    # 检测场景并获取动态上下文
    scenario = detect_scenario(user_input)
    scenario_prompt = get_scenario_specific_prompt(scenario)

    # 检查最近一轮对话中的工具结果是否包含引用块标签
    tool_results = []
    # 从后往前遍历消息，只取最近一轮的工具结果
    for message in reversed(state["messages"]):
        # 如果遇到 HumanMessage 或 AIMessage（非工具调用），说明到了上一轮对话，停止
        if message.type == "human" or (message.type == "ai" and not hasattr(message, 'tool_calls')):
            break
        # 如果是工具消息且包含引用块标签，添加到结果中
        if message.type == "tool" and hasattr(message, 'content') and message.content and ">" in message.content:
            tool_results.append(message.content)

    dynamic_context = get_dynamic_context(user_input, tool_results)

    # 始终创建新的提示词模板，确保完全隔离
    from langchain_core.prompts import ChatPromptTemplate
    from langchain_core.messages import SystemMessage

    # 获取原始系统提示
    original_system_prompt = prompt_template.messages[0].prompt.template

    # 根据是否有动态上下文决定最终的系统提示
    if dynamic_context or scenario_prompt:
        # 构建增强的系统提示
        final_system_prompt = f"""
{dynamic_context}

{scenario_prompt}

{original_system_prompt}
"""
    else:
        # 使用原始系统提示
        final_system_prompt = original_system_prompt

    # 始终创建新的提示词模板，确保不会有状态污染
    current_prompt_template = ChatPromptTemplate.from_messages([
        ("system", final_system_prompt),
        ("placeholder", "{messages}")
    ])

    # 创建链
    if len(tools_to_use) > 0:
        chain = current_prompt_template | llm.bind_tools(tools=tools_to_use)
    else:
        chain = current_prompt_template | llm

    accumulated_chunk: AIMessageChunk = AIMessageChunk(content="")
    
    def create_result_message() -> AIMessage:
        """创建AIMessage对象"""
        return AIMessage(
            content=accumulated_chunk.content,
            tool_calls=accumulated_chunk.tool_calls,
            invalid_tool_calls=accumulated_chunk.invalid_tool_calls,
            response_metadata=accumulated_chunk.response_metadata,
            usage_metadata=accumulated_chunk.usage_metadata,
            id=accumulated_chunk.id
        )

    try:
        async for chunk in chain.astream({"messages": state["messages"]}):
            accumulated_chunk += chunk
            
            # 检查是否有停止事件
            if thread_id and thread_id in stop_events and stop_events[thread_id].is_set():
                logger.info(f"检测到中断事件，thread_id: {thread_id}")
                break
                
        return {
            **state,
            "messages": [create_result_message()]
        }
    except GeneratorExit:
        logger.info(f"检测到中断事件（GeneratorExit），thread_id: {thread_id}")
        return {
            **state,
            "messages": [create_result_message()]
        }
    except Exception as e:
        logger.error(f"模型调用时出错: {e}", exc_info=True)
        return {
            **state,
            "messages": [create_result_message()]
        }

async def generate_title(state: State):
    """
    根据对话内容生成标题
    
    Args:
        state: 当前对话状态
        
    Returns:
        dict: 包含生成的标题的状态更新
    """
    chain = title_prompt_template | title_llm

    conversation_messages = [
        message 
        for message in state["messages"]  
        if message.type in ("human", "system")
        or (message.type == "ai" and not message.tool_calls)]
    result = await chain.ainvoke({"messages": conversation_messages})
    return {
        "title": result.content,
        "title_generated": True,
        "messages": []
    } 

async def human_review(state: State) -> Command[Literal["model", "tools", "__end__"]]:
    """
    处理人工审核节点
    根据不同工具类型分配不同的处理策略
    
    Args:
        state: 当前对话状态
        
    Returns:
        Command: 下一步执行的命令
    """
    tool_call = state["messages"][-1].tool_calls[0]
    tool_name = tool_call["name"]
    
    # 根据工具名称获取对应的处理策略
    strategy = strategy_factory.get_strategy(tool_name)
    return await strategy.handle_interrupt(tool_call)

def node_router(state: State):
    """
    节点路由器，决定下一步执行哪个节点
    
    Args:
        state: 当前对话状态
        
    Returns:
        str: 下一个节点名称
    """
    # 判断是否要工具
    node = tools_condition(state)
    # 判断是否要生成标题
    if node == END and state["title_generated"] == False:
        return "generate_title"
    elif node == END:
        return END
    
    return "human_review_node" 


def simple_intent_analysis(state: State):
    """
    简单意图分析
    """
    last_message = state["messages"][-1]
    if last_message.type != "human":
        return "other"
    
    content = last_message.content
    if any(keyword in content.lower() for keyword in resource_related_keywords):
        return "resource_related"
    elif any(keyword in content.lower() for keyword in hardware_related_keywords):
        return "hardware_related"
    else:
        return "other"
