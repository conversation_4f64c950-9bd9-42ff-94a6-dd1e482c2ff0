"""
工具模块
提供各种功能工具的统一导入
"""
from tools.common_tools import (
    get_user_info_from_config, 
    get_managed_bars_by_user, 
    query_bar_details,
    query_bar_by_id,
    get_current_time
)
from tools.game_tools import (
    query_resource_presence, 
    get_resource_installation_status, 
    initiate_resource_download_or_update, 
    get_recent_resource_updates_info, 
    get_resource_update_status, 
    get_bars_with_failed_update
)
from tools.hardware_tools import (
    get_bar_hardware_overview
    # count_machines_by_component_filter_in_bars,
    # find_machines_by_criteria_in_bars,
    # find_bars_by_hardware_profile
)

# 定义工具列表
game_tools = [
    query_resource_presence,
    get_resource_installation_status,
    initiate_resource_download_or_update,
    get_recent_resource_updates_info,
    get_resource_update_status,
    get_bars_with_failed_update
]

common_tools = [
    get_user_info_from_config,
    get_managed_bars_by_user,
    query_bar_details,
    query_bar_by_id,
    get_current_time
]

hardware_tools = [
    get_bar_hardware_overview
    # count_machines_by_component_filter_in_bars,
    # find_machines_by_criteria_in_bars,
    # find_bars_by_hardware_profile
]

# 所有工具的集合
all_tools = game_tools + common_tools + hardware_tools

resource_related_tools = game_tools + common_tools
hardware_related_tools = hardware_tools + common_tools

time_tools = [
    get_current_time
]