#!/usr/bin/env python3
"""
测试指定网吧下载游戏的表格格式修复
确保指定网吧下载游戏时展示的是带标签的表格格式，而不是列表
"""

def test_download_table_format_fix():
    """测试指定网吧下载游戏的表格格式修复"""
    
    print("=" * 80)
    print("指定网吧下载游戏表格格式修复测试")
    print("=" * 80)
    
    print("\n🎯 问题描述:")
    print("用户在指定网吧下载游戏时，流程是对了，但是表格展示的不对")
    print("要求的是表格和其他所有调用了query_resource_presence工具的流程一样，展示标签+表格")
    print("但是提示词导致AI胡乱展示了一个列表")
    
    print("\n🔍 问题分析:")
    
    problem_analysis = {
        "正确流程": "用户指定网吧下载游戏 → 先调用query_resource_presence → 展示游戏列表",
        "格式问题": "AI展示了列表格式而不是带标签的表格格式",
        "期望格式": "与其他query_resource_presence调用一样的带标签表格格式",
        "根本原因": "提示词没有明确说明指定网吧下载时也要保持工具返回的格式"
    }
    
    for key, value in problem_analysis.items():
        print(f"{key}: {value}")
    
    print("\n" + "=" * 80)
    print("🔧 修复方案")
    print("=" * 80)
    
    fixes = [
        {
            "修复位置": "TOOL_CALLING_RESTRICTIONS - 格式要求",
            "添加内容": "格式要求：指定网吧下载游戏时调用query_resource_presence，必须返回带标签的表格格式（如 > download），与其他query_resource_presence调用保持一致",
            "目的": "明确指定网吧下载时的格式要求"
        },
        {
            "修复位置": "THINKING_PROCESS - 下载更新流程检查",
            "修改内容": "如果用户要下载/更新游戏且指定了网吧，我必须先调用query_resource_presence展示带标签的游戏列表（如 > download）",
            "目的": "在思考过程中强调带标签格式"
        },
        {
            "修复位置": "输出处理规则 - 特别注意",
            "添加内容": "特别注意: 指定网吧下载游戏时，query_resource_presence返回的带标签内容也必须100%原样输出",
            "目的": "确保指定网吧下载时也保持工具返回格式"
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"\n{i}. {fix['修复位置']}:")
        print(f"   添加/修改内容: {fix.get('添加内容', fix.get('修改内容'))}")
        print(f"   目的: {fix['目的']}")
    
    print("\n" + "=" * 80)
    print("🧪 测试场景对比")
    print("=" * 80)
    
    test_scenarios = [
        {
            "场景": "普通游戏查询",
            "用户输入": "查询英雄联盟",
            "工具调用": "query_resource_presence",
            "期望输出": "带 > download 标签的表格格式",
            "当前状态": "✅ 正确"
        },
        {
            "场景": "游戏状态查询",
            "用户输入": "英雄联盟的安装状态",
            "工具调用": "query_resource_presence",
            "期望输出": "带 > query-resource-install-status 标签的表格格式",
            "当前状态": "✅ 正确"
        },
        {
            "场景": "指定网吧下载游戏（问题场景）",
            "用户输入": "在网吧A下载英雄联盟",
            "工具调用": "query_resource_presence",
            "期望输出": "带 > download 标签的表格格式",
            "当前状态": "❌ 错误（显示列表格式）"
        },
        {
            "场景": "指定网吧更新游戏（问题场景）",
            "用户输入": "网吧1109761更新穿越火线",
            "工具调用": "query_resource_presence",
            "期望输出": "带 > download 标签的表格格式",
            "当前状态": "❌ 错误（显示列表格式）"
        }
    ]
    
    print("\n测试场景对比:")
    for scenario in test_scenarios:
        print(f"\n• {scenario['场景']}:")
        print(f"  用户输入: '{scenario['用户输入']}'")
        print(f"  工具调用: {scenario['工具调用']}")
        print(f"  期望输出: {scenario['期望输出']}")
        print(f"  当前状态: {scenario['当前状态']}")
    
    print("\n" + "=" * 80)
    print("📋 格式对比示例")
    print("=" * 80)
    
    print("\n❌ 错误的输出格式（当前问题）:")
    wrong_format = """我来帮您在网吧A下载英雄联盟。首先让我查询英雄联盟的相关资源。

找到以下英雄联盟相关资源：
1. 英雄联盟 (ID: 12345) - 顺网 - 8.5GB
2. 英雄联盟美服 (ID: 12346) - 盖伦 - 8.2GB
3. 英雄联盟手游 (ID: 12347) - 顺网 - 3.1GB

请选择您要下载的版本，回复对应的编号。"""
    
    print(wrong_format)
    
    print("\n✅ 正确的输出格式（应该这样）:")
    correct_format = """我来帮您在网吧A下载英雄联盟。首先让我查询英雄联盟的相关资源。

根据您的查询 '英雄联盟'，为您匹配到以下资源：

> download

|资源ID|游戏名称|来源|大小|
|---|---|---|---|
|12345|英雄联盟|顺网|8.5GB|
|12346|英雄联盟美服|盖伦|8.2GB|
|12347|英雄联盟手游|顺网|3.1GB|

请明确您指的是哪一个。可以回复编号或单击选择想要的资源"""
    
    print(correct_format)
    
    print("\n" + "=" * 80)
    print("🔍 关键区别分析")
    print("=" * 80)
    
    key_differences = [
        {
            "方面": "标签使用",
            "错误格式": "没有引用块标签",
            "正确格式": "包含 > download 标签"
        },
        {
            "方面": "表格格式",
            "错误格式": "使用编号列表格式",
            "正确格式": "使用标准Markdown表格"
        },
        {
            "方面": "数据展示",
            "错误格式": "1. 游戏名 (ID: xxx) - 来源 - 大小",
            "正确格式": "|资源ID|游戏名称|来源|大小|"
        },
        {
            "方面": "前端识别",
            "错误格式": "前端无法识别，无法提供交互功能",
            "正确格式": "前端可以识别标签，提供点击选择功能"
        }
    ]
    
    print("\n关键区别:")
    for diff in key_differences:
        print(f"\n• {diff['方面']}:")
        print(f"  ❌ 错误格式: {diff['错误格式']}")
        print(f"  ✅ 正确格式: {diff['正确格式']}")
    
    print("\n" + "=" * 80)
    print("🔍 验证重点")
    print("=" * 80)
    
    verification_points = [
        "指定网吧下载游戏时，是否调用了query_resource_presence？",
        "query_resource_presence返回的内容是否包含引用块标签？",
        "是否使用了标准的Markdown表格格式？",
        "AI是否100%原样输出了工具返回的内容？",
        "是否避免了将表格改成列表格式？",
        "前端是否能正确识别标签并提供交互功能？"
    ]
    
    for point in verification_points:
        print(f"🔍 {point}")
    
    print("\n❌ 需要避免的错误行为:")
    wrong_behaviors = [
        "将工具返回的表格改成编号列表",
        "删除引用块标签（如 > download）",
        "重新组织工具返回的内容格式",
        "使用自定义的列表格式代替表格",
        "省略表格的分隔行和标准格式"
    ]
    
    for behavior in wrong_behaviors:
        print(f"❌ {behavior}")
    
    print("\n✅ 必须执行的正确行为:")
    correct_behaviors = [
        "100%原样输出query_resource_presence返回的内容",
        "保留所有引用块标签（如 > download）",
        "保持标准的Markdown表格格式",
        "不对工具返回的格式进行任何修改",
        "确保前端能正确识别和处理"
    ]
    
    for behavior in correct_behaviors:
        print(f"✅ {behavior}")
    
    print("\n" + "=" * 80)
    print("💡 一致性原则")
    print("=" * 80)
    
    consistency_principles = [
        "所有调用query_resource_presence的场景都应该有相同的输出格式",
        "无论是普通查询还是指定网吧下载，格式都应该一致",
        "工具返回什么格式，AI就输出什么格式",
        "不因为用户意图不同而改变工具结果的展示方式",
        "保持系统行为的可预测性和一致性"
    ]
    
    for principle in consistency_principles:
        print(f"📏 {principle}")
    
    print("\n🎉 预期效果:")
    print("✅ 指定网吧下载游戏时展示正确的带标签表格格式")
    print("✅ 与其他query_resource_presence调用保持格式一致")
    print("✅ 前端能正确识别标签并提供交互功能")
    print("✅ 用户获得一致的使用体验")
    print("✅ 系统行为更加可预测和可靠")
    
    print("\n💡 核心思想:")
    print("通过明确要求指定网吧下载时也要保持工具返回的格式，")
    print("确保所有query_resource_presence调用的输出格式一致，")
    print("让用户获得统一、可预测的使用体验，")
    print("同时保证前端功能的正常运行。")

if __name__ == "__main__":
    test_download_table_format_fix()
