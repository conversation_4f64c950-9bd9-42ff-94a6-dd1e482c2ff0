"""
Context Engineering - 动态上下文注入
根据不同的场景动态添加特定的约束和指令
"""

def get_dynamic_context(user_input: str, tool_results: list = None) -> str:
    """
    根据用户输入和工具结果动态生成上下文约束
    
    Args:
        user_input: 用户输入
        tool_results: 工具返回结果列表
    
    Returns:
        动态生成的上下文约束字符串
    """
    
    # 检测是否是游戏更新查询场景
    update_keywords = ["更新", "升级", "版本", "最新"]
    is_update_query = any(keyword in user_input for keyword in update_keywords)
    
    # 检测工具结果是否包含引用块标签
    has_quote_block = False
    if tool_results:
        for result in tool_results:
            if isinstance(result, str) and ">" in result and any(tag in result for tag in [
                "query-resource-update-status", 
                "download", 
                "update", 
                "query-resource-install-status",
                "update-resource-list",
                "bar-download",
                "bar-update"
            ]):
                has_quote_block = True
                break
    
    dynamic_context = ""
    
    # 如果是更新查询场景，添加特殊约束
    if is_update_query:
        dynamic_context += """
**特殊场景约束 - 游戏更新查询**
用户正在查询游戏更新情况。在这种场景下：
1. 工具可能返回包含 > query-resource-update-status 标签的内容
2. 你必须100%原样输出工具返回的所有内容
3. 绝对不允许重新组织、美化或修改工具返回的格式
4. 绝对不允许删除引用块标签
5. 绝对不允许将表格改成列表或其他格式

"""
    
    # 如果检测到工具结果包含引用块标签，添加强制约束
    if has_quote_block:
        dynamic_context += """
**检测到引用块标签 - 强制约束生效**
工具返回的内容包含引用块标签，这意味着：
1. 你必须将工具返回的内容完整复制粘贴作为最终输出
2. 不允许任何修改、重新组织或美化
3. 不允许删除任何标签、数据或格式
4. 不允许添加任何解释或补充说明
5. 就像执行 Ctrl+C 和 Ctrl+V 操作一样

这是最高优先级的要求，覆盖所有其他格式化指令！

"""
    
    return dynamic_context


def inject_context_before_llm(user_input: str, tool_results: list, base_prompt: str) -> str:
    """
    在调用LLM之前注入动态上下文
    
    Args:
        user_input: 用户输入
        tool_results: 工具返回结果
        base_prompt: 基础提示词
    
    Returns:
        注入动态上下文后的完整提示词
    """
    
    dynamic_context = get_dynamic_context(user_input, tool_results)
    
    if dynamic_context:
        # 在基础提示词的开头注入动态上下文
        enhanced_prompt = f"""
{dynamic_context}

{base_prompt}

{dynamic_context}
"""
        return enhanced_prompt
    
    return base_prompt


def get_scenario_specific_prompt(scenario: str) -> str:
    """
    获取特定场景的专用提示词
    
    Args:
        scenario: 场景类型 ('update_query', 'install_query', 'download_request', etc.)
    
    Returns:
        场景专用的提示词片段
    """
    
    scenario_prompts = {
        'update_query': """
你正在处理游戏更新查询。在这种情况下：
- 工具会返回包含 > query-resource-update-status 标签的表格
- 你必须完整保留工具返回的所有内容
- 绝对不允许重新组织或修改格式
- 绝对不允许删除标签或数据
- 绝对不允许将表格改成列表

示例正确输出：
根据您的查询 'XX游戏'，为您匹配到以下资源：

> query-resource-update-status

| 资源ID | 游戏名称 | 来源 | 大小 |
|---|---|---|---|
| 12345 | XX游戏 | 顺网 | 8.5GB |

请明确您指的是哪一个。可以回复编号或单击选择想要的资源
""",
        
        'install_query': """
你正在处理游戏安装状态查询。在这种情况下：
- 工具会返回包含 > query-resource-install-status 标签的表格
- 你必须完整保留工具返回的所有内容
- 保持表格格式，保持所有标签，保持所有数据
""",
        
        'download_request': """
你正在处理游戏下载请求。在这种情况下：
- 工具会返回包含 > download 标签的表格
- 你必须完整保留工具返回的所有内容
- 保持表格格式，保持所有标签，保持所有数据
"""
    }
    
    return scenario_prompts.get(scenario, "")


def detect_scenario(user_input: str) -> str:
    """
    检测用户输入的场景类型
    
    Args:
        user_input: 用户输入
    
    Returns:
        场景类型字符串
    """
    
    user_input_lower = user_input.lower()
    
    # 更新查询场景
    if any(keyword in user_input_lower for keyword in ["更新", "升级", "版本", "最新"]):
        return "update_query"
    
    # 安装状态查询场景
    if any(keyword in user_input_lower for keyword in ["安装", "状态", "情况"]):
        return "install_query"
    
    # 下载请求场景
    if any(keyword in user_input_lower for keyword in ["下载", "安装"]):
        return "download_request"
    
    return "general"


# 使用示例
def example_usage():
    """使用示例"""
    
    user_input = "查询穿越火线更新情况"
    scenario = detect_scenario(user_input)
    print(f"检测到场景: {scenario}")
    
    scenario_prompt = get_scenario_specific_prompt(scenario)
    print(f"场景专用提示词: {scenario_prompt}")
    
    # 模拟工具返回结果
    tool_results = ["""根据您的查询 '穿越火线'，为您匹配到以下资源：

> query-resource-update-status

| 资源ID | 游戏名称 | 来源 | 大小 |
|---|---|---|---|
| 11004 | 穿越火线 | 顺网 | 27812.57MB |

请明确您指的是哪一个。可以回复编号或单击选择想要的资源"""]
    
    dynamic_context = get_dynamic_context(user_input, tool_results)
    print(f"动态上下文: {dynamic_context}")


if __name__ == "__main__":
    example_usage()
