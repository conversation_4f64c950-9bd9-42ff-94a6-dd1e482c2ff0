"""
通用工具模块
提供用户信息和网吧信息查询等通用工具
"""
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import os
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from utils.api_utils import call_api, get_user_info, get_managed_bars
from datetime import datetime
# 配置日志
logger = logging.getLogger(__name__)

@tool
def get_user_info_from_config(config: RunnableConfig) -> str:
    """
        查询用户信息
    """
    try:
        # 检查是否为有效的配置对象
        if not config:
            message = "未能从当前会话配置中找到有效的配置信息。"
            return message
        
        # 获取configurable字段
        configurable = config.get("configurable", {})
        
        # 记录调试信息
        logger.debug(f"Config对象: {config}")
        logger.debug(f"Configurable: {configurable}")
        
        # 获取用户ID和线程ID
        user_id = configurable.get("user_id")
        thread_id = configurable.get("thread_id")
        brand_id = configurable.get("brand_id", 1)  # 默认品牌ID为1
        
        if not user_id:
            message = "未能从当前会话配置中找到有效的用户ID。"
            return message
        
        # 调用封装的API获取用户信息
        api_result = get_user_info(user_id, brand_id)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"获取用户信息失败：{error_msg}。当前会话的用户ID是 '{user_id}'"
            if thread_id:
                message += f"，线程ID是 '{thread_id}'"
            return message

        api_data = api_result.get('data')
        
        # 使用API返回的数据
        real_name = api_data.get('realName', '未知')
        kedou_id = api_data.get('kedouId', '未知')
        
        message = f"当前会话的用户ID是 '{user_id}'"
        if thread_id:
            message += f"，线程ID是 '{thread_id}'"
        message += f"，用户名是 '{real_name}'，通行证账号是 '{kedou_id}'。"
        
        return message
    
    except Exception as e:
        error_msg = f"获取用户信息时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool(response_format="content_and_artifact")
def get_managed_bars_by_user(config: RunnableConfig) -> Tuple[str, Dict[str, Any]]:
    """
    查询用户管理的所有网吧列表，并获取每个网吧【当前的实时在线状态】。这是一个高时效性查询，因为在线状态随时会变。
    """
    try:
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        # 调用封装的API获取用户管理的网吧信息
        api_result = get_managed_bars(user_id, brand_id)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"获取用户管理的网吧信息失败：{error_msg}"
            data = {"user_id_queried": user_id, "managed_bars_count": 0, "managed_bars": [], "error": error_msg}
            return message, data

        api_data = api_result.get('data')
        
        # 使用API返回的数据
        formatted_bars = []
        for bar in api_data:
            formatted_bar = {
                "bar_name": bar.get("barName", "未知"),
                "bar_id": bar.get("barId", 0),
                "dll_online_state": "在线" if bar.get("dllOnlineState") == 1 else "离线",
            }
            formatted_bars.append(formatted_bar)
        
        bar_count = len(formatted_bars)
        
        result_dict = {
            "user_id_queried": user_id,
            "managed_bars_count": bar_count,
            "managed_bars": formatted_bars,
            "data_source": "api"
        }
        
        if bar_count == 0:
            message = f"用户ID【{user_id}】当前未管理任何网吧。"
            return message, result_dict
        
        # 构建自然语言消息
        bar_descriptions = []
        for i, bar in enumerate(formatted_bars, 1):
            bar_desc = f"{i}. 【{bar['bar_name']}】(ID: {bar['bar_id']}"
            if "dll_online_state" in bar:
                bar_desc += f", 状态: {bar['dll_online_state']}"
            
            bar_desc += ")"
            bar_descriptions.append(bar_desc)
        
        # 调用封装的API获取用户信息
        user_result = get_user_info(user_id, brand_id)
        user_data = user_result.get('data') if user_result.get('success') else None
        user_name = user_data.get('realName', '未知用户') if user_data else "未知用户"
        
        message = f"用户【{user_name}】(ID: {user_id})共管理以下 {bar_count} 家网吧：" + ", ".join(bar_descriptions) + "。"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取用户管理的网吧信息时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "user_id_queried": user_id}


@tool(response_format="content_and_artifact")
def query_bar_by_id(bar_id: int, config: RunnableConfig) -> Tuple[str, Dict[str, Any]]:
    """
    根据网吧ID查询网吧详情
    
    Args:
        bar_id: 网吧的唯一ID
    """
    try:
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        # 调用封装的API获取指定ID的网吧信息
        api_result = get_managed_bars(user_id, brand_id, bar_id=bar_id)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"未找到ID为【{bar_id}】的网吧，或您没有权限查看该网吧。错误信息：{error_msg}"
            return message, {"bar_id_queried": bar_id, "is_found": False, "error": error_msg}

        api_data = api_result.get('data')
        
        # 在返回的数据中查找指定ID的网吧
        matched_bar = None
        for bar in api_data:
            if bar.get('barId') == bar_id:
                matched_bar = bar
                break
        
        if not matched_bar:
            message = f"未找到ID为【{bar_id}】的网吧，或您没有权限查看该网吧。"
            return message, {"bar_id_queried": bar_id, "is_found": False}
        
        # 格式化网吧信息
        formatted_bar = {
            "bar_name": matched_bar.get("barName", "未知"),
            "bar_id": matched_bar.get("barId", 0),
            "dll_online_state": "在线" if matched_bar.get("dllOnlineState") == 1 else "离线",
        }
        
        result_dict = {
            "bar_id_queried": bar_id,
            "is_found": True,
            "bar_details": formatted_bar
        }
        
        # 构建自然语言消息
        message = f"网吧ID【{bar_id}】的详细信息如下：\n"
        message += f"名称：【{formatted_bar['bar_name']}】\n"
        message += f"状态：{formatted_bar['dll_online_state']}\n"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"查询网吧详情时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "bar_id_queried": bar_id}


@tool(response_format="content_and_artifact")
def query_bar_details(bar_name_query: str, config: RunnableConfig) -> Tuple[str, Dict[str, Any]]:
    """
    根据网吧名称查询网吧详情
    
    Args:
        bar_name_query: 网吧名称或部分名称
    """
    try:
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        # 调用封装的API获取匹配名称的网吧信息
        api_result = get_managed_bars(user_id, brand_id, bar_name=bar_name_query)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"未找到匹配查询【{bar_name_query}】的网吧。错误信息：{error_msg}"
            return message, {"bar_name_queried": bar_name_query, "is_found": False, "matched_bars": [], "error": error_msg}

        api_data = api_result.get('data')
        
        # 格式化网吧信息
        formatted_bars = []
        for bar in api_data:
            formatted_bar = {
                "bar_name": bar.get("barName", "未知"),
                "bar_id": bar.get("barId", 0),
                "dll_online_state": "在线" if bar.get("dllOnlineState") == 1 else "离线",
            }
            formatted_bars.append(formatted_bar)
        
        result_dict = {
            "bar_name_queried": bar_name_query,
            "is_found": len(formatted_bars) > 0,
            "matched_bars": formatted_bars
        }
        
        if not result_dict["is_found"]:
            message = f"未找到匹配查询【{bar_name_query}】的网吧。"
            return message, result_dict
        
        # 构建自然语言消息
        if len(formatted_bars) == 1:
            bar = formatted_bars[0]
            message = f"找到网吧【{bar['bar_name']}】(ID: {bar['bar_id']})的详细信息如下：\n"
            message += f"状态：{bar['dll_online_state']}\n"
        else:
            message = f"查询【{bar_name_query}】找到以下 {len(formatted_bars)} 家网吧：\n"
            for i, bar in enumerate(formatted_bars, 1):
                message += f"{i}. 【{bar['bar_name']}】(ID: {bar['bar_id']}, 状态: {bar['dll_online_state']})\n"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"查询网吧详情时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "bar_name_queried": bar_name_query}
@tool
def get_current_time():
    """
    获取当前时间
    """
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def _create_mock_config(user_id: int) -> RunnableConfig:
    """
    创建一个用于测试的模拟配置对象
    
    Args:
        user_id: 用户ID
        
    Returns:
        RunnableConfig: 模拟的配置对象
    """
    return {
        "configurable": {
            "user_id": user_id,
            "thread_id": "test-thread-id"
        }
    }


def run_simple_test():
    """简单的测试函数"""
    # 创建一个测试配置
    test_config = _create_mock_config(1)
    
    # 测试获取用户信息
    print("\n测试获取用户信息:")
    user_info = get_user_info_from_config(test_config)
    print(user_info)
    
    # 测试获取用户管理的网吧
    print("\n测试获取用户管理的网吧:")
    bars_message, bars_data = get_managed_bars_by_user(test_config)
    print(bars_message)
    print(json.dumps(bars_data, ensure_ascii=False, indent=2))
    
    # 测试根据ID查询网吧
    print("\n测试根据ID查询网吧:")
    bar_id_message, bar_id_data = query_bar_by_id(1, test_config)
    print(bar_id_message)
    print(json.dumps(bar_id_data, ensure_ascii=False, indent=2))
    
    # 测试根据名称查询网吧
    print("\n测试根据名称查询网吧:")
    bar_name_message, bar_name_data = query_bar_details("清风", test_config)
    print(bar_name_message)
    print(json.dumps(bar_name_data, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    run_simple_test() 