"""
API工具模块
提供API调用相关的工具函数
"""
import os
import requests
import hashlib
import datetime
import logging
# 配置日志
logger = logging.getLogger(__name__)

# 确保配置已加载的标志
_config_loaded = False

def _ensure_config_loaded():
    """确保配置已经加载"""
    global _config_loaded
    if not _config_loaded:
        try:
            # 尝试导入并调用配置加载函数
            from config.app_config import load_config
            load_config()
            _config_loaded = True
            logger.debug("API工具模块：配置已加载")
        except Exception as e:
            logger.warning(f"API工具模块：配置加载失败，将使用默认值: {e}")
            _config_loaded = True  # 标记为已尝试，避免重复尝试

# API配置函数
def get_api_base_url():
    """获取API基础URL"""
    _ensure_config_loaded()
    return os.environ.get("WANGWEI_API_BASE_URL", "https://api.example.com")

def get_api_md5_key():
    """获取API MD5密钥"""
    _ensure_config_loaded()
    return os.environ.get("WANGWEI_API_MD5_KEY", "md5key")


def generate_timestamp():
    """生成格式为yyyyMMddHHmmss的时间戳"""
    return datetime.datetime.now().strftime("%Y%m%d%H%M%S")

def generate_sign(params_dict):
    """
    生成API签名
    1. 按照键名英文升序排序参数
    2. 拼接MD5加密参数形式value1|value2|md5key，多个参数用"|"连接
    
    Args:
        params_dict: 参数字典
        
    Returns:
        str: 生成的MD5签名
    """
    # 按键名排序
    sorted_params = sorted(params_dict.items(), key=lambda x: x[0])
    
    # 拼接参数值，处理None值为空字符串
    values = []
    for _, v in sorted_params:
        if v is None:
            values.append("")
        else:
            values.append(str(v))
    values.append(get_api_md5_key())
    
    # 生成签名字符串
    sign_str = "|".join(values)
    
    # MD5加密并转小写
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    
    return sign

def call_api(endpoint, params):
    """
    调用API并返回结果
    
    Args:
        endpoint: API端点路径
        params: 请求参数
        
    Returns:
        dict: API返回的数据，如果调用失败则返回None
    """
    try:
        # 添加时间戳
        params['time'] = generate_timestamp()
        
        # 生成签名
        sign = generate_sign(params)
        params['sign'] = sign
        
        # 发送请求
        url = f"{get_api_base_url()}{endpoint}"
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        
        # 检查API返回状态
        if data.get('code') != 0 or not data.get('success'):
            logger.error(f"API调用失败: {data.get('msg')}")
            return None
        
        return data.get('data')
    
    except Exception as e:
        logger.error(f"API调用异常: {str(e)}")
        return None

# 封装的API调用函数
def get_user_info(user_id, brand_id=1):
    """
    获取用户信息
    
    Args:
        user_id: 用户ID
        brand_id: 品牌ID，默认为1
        
    Returns:
        dict: 用户信息，如果调用失败则返回None
    """
    params = {
        'frontUserId': user_id,
        'brandId': brand_id
    }
    return call_api('/wangwei/sign/getFrontUserInfo', params)

def get_managed_bars(user_id, brand_id=1, bar_id=None, bar_name=None):
    """
    获取用户管理的网吧列表
    
    Args:
        user_id: 用户ID
        brand_id: 品牌ID，默认为1
        bar_id: 网吧ID，可选，用于筛选特定网吧
        bar_name: 网吧名称，可选，用于按名称筛选网吧
        
    Returns:
        list: 网吧列表，如果调用失败则返回None
    """
    params = {
        'frontUserId': user_id,
        'brandId': brand_id
    }
    
    # 添加可选参数
    if bar_id is not None:
        params['barId'] = bar_id
    
    if bar_name is not None:
        params['barName'] = bar_name
    
    return call_api('/wangwei/sign/queryUserManagedBars', params)

def get_bar_assets(user_id, bar_id, brand_id=1, asset_item_type=None):
    """
    获取网吧资产信息
    
    Args:
        user_id: 用户ID
        bar_id: 单个网吧ID
        brand_id: 品牌ID，默认为1
        asset_item_type: 资产项类型，可选：0-CPU 1-内存 2-GPU 3-主板
        
    Returns:
        list: 网吧资产信息列表，如果调用失败则返回None
    """
    
    
    params = {
        'frontUserId': user_id,
        'brandId': brand_id,
        'barId': bar_id
    }
    
    # 添加可选参数
    if asset_item_type is not None:
        params['assetItemTypes'] = asset_item_type
    return call_api('/wangwei/sign/countBarAssets', params)

def search_resources(user_id, brand_id=1, resource_id=None, keyword=None, resource_origin=None):
    """
    搜索游戏资源
    
    Args:
        user_id: 用户ID
        brand_id: 品牌ID，默认为1
        resource_id: 资源ID，可选，用于查询特定资源
        keyword: 关键词，可选，用于按名称搜索资源
        
    Returns:
        dict: 包含资源列表的字典，如果调用失败则返回None
    """
    params = {
        'frontUserId': user_id,
        'brandId': brand_id
    }
    
    # 添加可选参数
    if resource_id is not None:
        params['resourceId'] = resource_id
    
    if keyword is not None:
        params['keyword'] = keyword
    
    if resource_origin is not None:
        params['resourceOrigin'] = resource_origin
        
    return call_api('/wangwei/sign/searchResource', params)

def batch_download_resources(user_id, resource_ids, bar_ids, resource_origin, brand_id=1):
    """
    批量下载资源到网吧
    
    Args:
        user_id: 用户ID
        resource_ids: 资源ID列表或单个资源ID
        bar_ids: 网吧ID列表或单个网吧ID
        resource_origin: 游戏来源，如"顺网"、"盖伦"、"好司机"、"小蘑菇"
        brand_id: 品牌ID，默认为1
        
    Returns:
        bool: API调用是否成功 (True表示成功，False表示失败)
    """
    # 处理resource_ids参数，确保它是字符串格式
    if isinstance(resource_ids, list):
        resource_ids_str = ','.join(map(str, resource_ids))
    else:
        resource_ids_str = str(resource_ids)
    
    # 处理bar_ids参数，确保它是字符串格式
    if isinstance(bar_ids, list):
        bar_ids_str = ','.join(map(str, bar_ids))
    else:
        bar_ids_str = str(bar_ids)
    
    params = {
        'frontUserId': user_id,
        'brandId': brand_id,
        'resourceIds': resource_ids_str,
        'barIds': bar_ids_str,
        'resourceOrigin': resource_origin
    }
    
    try:
        # 添加时间戳
        params['time'] = generate_timestamp()
        
        # 生成签名
        sign = generate_sign(params)
        params['sign'] = sign
        
        # 发送POST请求
        url = f"{get_api_base_url()}/wangwei/sign/batchDownloadResourceToBars"
        response = requests.post(url, data=params)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        
        # 只关注code和success字段
        is_success = data.get('code') == 0 and data.get('success') == True
        
        if not is_success:
            logger.error(f"批量下载API调用失败: {data.get('msg')}")
        
        return is_success
    
    except Exception as e:
        logger.error(f"批量下载资源API调用异常: {str(e)}")
        return False

def query_bar_resource_download_result(user_id, resource_id, resource_origin, brand_id=1):
    """
    获取网吧游戏下载结果
    
    Args:
        user_id: 用户ID
        resource_id: 资源ID
        resource_origin: 游戏来源，如"顺网"、"盖伦"、"好司机"、"小蘑菇"
        brand_id: 品牌ID，默认为1
        
    Returns:
        dict: 包含下载结果的字典，如果调用失败则返回None
              结构为: {
                "successBarNum": 已下载的网吧数量,
                "failBarNum": 未下载的网吧数量,
                "barList": [{"barId": 网吧ID, "barName": 网吧名称}, ...]
              }
    """
    params = {
        'frontUserId': user_id,
        'brandId': brand_id,
        'resourceId': resource_id,
        'resourceOrigin': resource_origin
    }
    
    return call_api('/wangwei/sign/queryBarResourceDownloadResult', params)

def query_bar_resource_update_result(user_id, resource_id, brand_id=1):
    """
    获取网吧游戏更新结果
    
    Args:
        user_id: 用户ID
        resource_id: 资源ID
        brand_id: 品牌ID，默认为1
        
    Returns:
        dict: 包含更新结果的字典，如果调用失败则返回None
              结构为: {
                "successBarNum": 更新完成的网吧数量,
                "failBarNum": 更新失败的网吧数量,
                "barList": [{"barId": 网吧ID, "barName": 网吧名称}, ...]
              }
    """
    params = {
        'frontUserId': user_id,
        'brandId': brand_id,
        'resourceId': resource_id
    }
    
    return call_api('/wangwei/sign/queryBarResourceUpdateResult', params)

def query_game_update_today():
    """
    查询今日更新游戏
    
    Returns:
        dict: 包含今日更新游戏的字典，如果调用失败则返回None
              结构为: {
                "resources": [
                    {
                        "resourceId": 资源ID,
                        "resourceName": 资源名称,
                        "resourceSourceType": 游戏来源类型,
                        "resourceOrigin": 游戏来源,
                        "resourceSize": 资源大小
                    },
                    ...
                ]
              }
    """
    params = {}
    
    return call_api('/wangwei/sign/queryGameUpdateToday', params)
