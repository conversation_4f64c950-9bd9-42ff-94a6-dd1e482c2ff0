"""
游戏管理工具模块
提供游戏资源的查询、下载和管理功能
"""
from typing import Dict, List, Tuple, Any, Union
import logging
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from datetime import datetime
from utils.api_utils import search_resources

# 配置日志
logger = logging.getLogger(__name__)

# 辅助函数：智能添加引用块标签
def smart_add_quote_block(prefix_text, tag, table_content):
    """
    智能添加引用块标签，避免双重换行符

    Args:
        prefix_text: 标签前的文本
        tag: 引用块标签（如 "download"）
        table_content: 表格内容

    Returns:
        格式化的完整字符串
    """
    # 检查前缀文本是否以换行符结尾
    if prefix_text.endswith('\n'):
        # 如果已经有换行符，直接添加标签
        return f"{prefix_text}> {tag}\n\n{table_content}"
    else:
        # 如果没有换行符，添加一个换行符再加标签
        return f"{prefix_text}\n> {tag}\n\n{table_content}"



@tool(response_format="content_and_artifact")
def query_resource_presence(resource_identifier: str, config: RunnableConfig, resource_origin: str, intent: str = "query_install_status", bar_specified: bool = False) -> Tuple[str, Dict[str, Any]]:
    """
    根据用户提供的资源标识（名称、别名、模糊关键词或ID），查询系统中是否存在匹配的资源

    Args:
        resource_identifier: 用户提供的资源标识，可以是资源名称（如 "英雄联盟"）、部分名称、别名或已知的资源ID
        config: 配置对象，包含用户ID等信息
        intent: 查询意图，可以是"query_install_status"(查询安装状态)、"query_update_status"(查询更新状态)、"update"(更新)或"download"(下载)，默认为"query_install_status"
        bar_specified: 用户是否在查询中指定了网吧，默认为False
        resource_origin: 游戏来源，如"顺网"、"盖伦"、"好司机"、"小蘑菇"。

    **重要输出要求**:
    此工具返回的格式化内容包含引用块标签（如 > query-resource-update-status）和完整的游戏数据表格。
    你必须将工具返回的内容**原封不动**地输出给用户，绝对不允许：
    - 删除或省略引用块标签
    - 删除或省略任何游戏数据行
    - 修改表格格式
    - 重新组织内容
    工具返回什么就输出什么，一个字符都不能少！
    """
    try:
        # 验证intent参数
        if intent not in ["query_install_status", "query_update_status", "download", "update"]:
            intent = "query_install_status"  # 默认为查询安装状态意图

        if intent == "query_update_status":
            resource_origin = "顺网"

        # 获取用户ID和品牌ID
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        if not user_id:
            message = "未能从当前会话配置中找到有效的用户ID。"
            return message, {"error": "用户ID不存在", "query_term": resource_identifier, "bar_specified": bar_specified}
        
        # 根据标识符类型决定如何查询
        if resource_identifier.isdigit():
            # 如果是数字，则按资源ID查询
            api_result = search_resources(user_id, brand_id, resource_id=int(resource_identifier), resource_origin=resource_origin)
        else:
            # 否则按关键词查询
            api_result = search_resources(user_id, brand_id, keyword=resource_identifier, resource_origin=resource_origin)
        
        # 检查API调用是否成功
        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"查询资源失败：{error_msg}"
            return message, {"error": error_msg, "query_term": resource_identifier}

        # 从API结果中提取数据
        api_data = api_result.get('data')

        # 从API结果中提取资源列表
        matched_resources = api_data.get("resources", []) if api_data else []
        
        # 准备结构化数据
        result_dict = {
            "query_term": resource_identifier,
            "is_found": len(matched_resources) > 0,
            "matched_resources": [],
            "intent": intent,
            "bar_specified": bar_specified,
            "third_download_tool_enabled": api_result.get("thirdDownloadToolShouldEnabled", False)
        }
        
        # 格式化匹配的资源
        for resource in matched_resources:
            formatted_resource = {
                "resource_name": resource.get("resourceName", "未知"),
                "resource_id": resource.get("resourceId", 0),
                "resource_source_type": resource.get("resourceSourceType", 0),
                "resource_origin": resource.get("resourceOrigin", "未知"),
                "resource_size": resource.get("resourceSize", 0)
            }
            result_dict["matched_resources"].append(formatted_resource)
        
        # 构建自然语言消息
        if not result_dict["is_found"]:
            message = f"未能根据您的查询 '{resource_identifier}' 找到任何匹配资源。"
            
        elif len(matched_resources) == 1:
            # 单个匹配结果也使用标签
            resource = matched_resources[0]
            resource_name = resource.get("resourceName", "未知")
            resource_id = resource.get("resourceId", 0)
            resource_origin = resource.get("resourceOrigin", "") or ""
            resource_size_mb = resource.get("resourceSize", 0) / 1024  # 转换为MB
            
            # 确定意图代码
            intent_code = "query-resource-install-status"
            if intent == "download":
                intent_code = "bar-download" if bar_specified else "download"
            elif intent == "query_update_status":
                intent_code = "query-resource-update-status"
            elif intent == "update":
                intent_code = "bar-update" if bar_specified else "update"
            
            # 构建表格内容
            table_content = "| 资源ID | 游戏名称 | 来源 | 大小 |\n"
            table_content += "|---|---|---|---|\n"
            table_content += f"| {resource_id} | {resource_name} | {resource_origin} | {resource_size_mb:.2f}MB |\n"

            # 使用智能函数添加引用块标签
            prefix_text = "已为您精确找到资源："
            message = smart_add_quote_block(prefix_text, intent_code, table_content)

            # 构建格式化的资源列表（用于结构化数据）
            formatted_list = f"> {intent_code}\n\n{table_content}"
            result_dict["formatted_resource_list"] = formatted_list
        else:
            # 对于多个匹配结果，使用根据意图确定的标签格式化列表
            prefix_text = f"根据您的查询 '{resource_identifier}'，为您匹配到以下资源："

            # 确定意图代码
            intent_code = "query-resource-install-status"
            if intent == "download":
                intent_code = "bar-download" if bar_specified else "download"
            elif intent == "query_update_status":
                intent_code = "query-resource-update-status"
            elif intent == "update":
                intent_code = "bar-update" if bar_specified else "update"

            # 构建表格内容
            table_content = "| 资源ID | 游戏名称 | 来源 | 大小 |\n"
            table_content += "|---|---|---|---|\n"

            for resource in matched_resources:
                resource_name = resource.get("resourceName", "未知")
                resource_id = resource.get("resourceId", 0)
                resource_origin = resource.get("resourceOrigin", "") or ""
                resource_size_mb = resource.get("resourceSize", 0) / 1024  # 转换为MB
                # 确保资源大小始终显示单位MB
                table_content += f"| {resource_id} | {resource_name} | {resource_origin} | {resource_size_mb:.2f}MB |\n"

            # 使用智能函数添加引用块标签
            message = smart_add_quote_block(prefix_text, intent_code, table_content)
            message += "请明确您指的是哪一个。可以回复编号或单击选择想要的资源"

            # 构建格式化的资源列表（用于结构化数据）
            formatted_list = f"> {intent_code}\n\n{table_content}"
            result_dict["formatted_resource_list"] = formatted_list.strip()
        if result_dict.get("third_download_tool_enabled", False):
                message += "\n如果没有您满意的资源，您可以开启第三方下载工具"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"查询资源时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "query_term": resource_identifier, "bar_specified": bar_specified}


@tool(response_format="content_and_artifact")
def get_resource_installation_status(resource_id: int, config: RunnableConfig, resource_origin: str = "顺网") -> Tuple[str, Dict[str, Any]]:
    """
    查询指定资源在网吧的安装状态

    Args:
        resource_id: 资源的唯一ID，请先通过query_resource_presence获取
        resource_origin: 游戏来源，如"顺网"、"盖伦"、"好司机"、"小蘑菇"，默认为"顺网"

    **重要输出要求**:
    此工具返回带有引用块标签的状态表格，你必须原封不动地输出，不允许删除标签或修改格式！
    """
    try:
        # 获取用户ID和品牌ID
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        if not user_id:
            message = "未能从当前会话配置中找到有效的用户ID。"
            return message, {"error": "用户ID不存在", "resource_id": resource_id}
        
        # 验证resource_origin是否有效
        valid_origins = ["顺网", "盖伦", "好司机", "小蘑菇"]
        if resource_origin not in valid_origins:
            message = f"无效的游戏来源 '{resource_origin}'，有效值为: {', '.join(valid_origins)}"
            return message, {"error": "无效的游戏来源", "resource_id": resource_id}
        
        # 调用API获取资源信息
        from utils.api_utils import search_resources, query_bar_resource_download_result
        
        # 先查询资源信息，确认资源存在
        search_result = search_resources(user_id, brand_id, resource_id=resource_id, resource_origin=resource_origin)

        # 检查API调用是否成功
        if not search_result.get('success') or search_result.get('code') != 0:
            error_msg = search_result.get('msg', '未知错误')
            prefix_text = f"查询资源ID {resource_id} 失败：{error_msg}"
            table_content = "| 状态 | 数量 |\n|---|---|\n| 查询失败 | - |"
            message = smart_add_quote_block(prefix_text, "download", table_content)
            return message, {"resource_id_queried": resource_id, "error": error_msg}

        # 从API响应中获取data部分
        resource_info = search_result.get('data')
        if not resource_info or not resource_info.get("resources"):
            # 使用智能函数创建标准格式的表格，用于资源未找到的情况
            prefix_text = f"未找到ID为 {resource_id} 的资源。"
            table_content = "| 状态 | 数量 |\n|---|---|\n| 未找到资源 | - |"
            message = smart_add_quote_block(prefix_text, "download", table_content)
            return message, {"resource_id_queried": resource_id, "error": "资源ID不存在"}

        # 获取资源名称
        resource = resource_info["resources"][0]
        resource_name = resource.get("resourceName", f"未知资源(ID: {resource_id})")
        
        # 调用API获取网吧游戏下载结果
        api_result = query_bar_resource_download_result(user_id, resource_id, resource_origin, brand_id)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"获取资源《{resource_name}》(ID: {resource_id})的安装状态失败：{error_msg}"
            return message, {
                "resource_id_queried": resource_id,
                "resource_name": resource_name,
                "error": error_msg
            }

        result = api_result.get('data')
        
        # 处理API返回结果
        success_bar_num = result.get("successBarNum", 0)  # 已下载的网吧数量
        fail_bar_num = result.get("failBarNum", 0)  # 未下载的网吧数量
        bar_list = result.get("barList", [])  # 网吧列表
        
        # 准备结构化数据
        result_dict = {
            "resource_id_queried": resource_id,
            "resource_name": resource_name,
            "resource_origin": resource_origin,
            "success_bar_num": success_bar_num,
            "fail_bar_num": fail_bar_num,
            "bar_list": bar_list
        }
        
        # 按照STATUS_DISPLAY_RULES构建带标签的表格格式
        prefix_text = f"资源《{resource_name}》(ID: {resource_id})的安装状态："

        # 构建状态表格内容
        table_content = "| 状态 | 数量 |\n"
        table_content += "|---|---|\n"
        table_content += f"| 已安装网吧 | {success_bar_num} |\n"
        table_content += f"| 未安装网吧 | {fail_bar_num} |\n"

        # 使用智能函数添加引用块标签
        message = smart_add_quote_block(prefix_text, "download", table_content)

        # 添加标准提醒和操作提示
        message += "\n*以上数据仅统计网维Online在线的网吧。*\n\n"
        message += "**您可以点击'未安装网吧'的数量，为这些网吧重新下载该资源。**"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取资源安装状态时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "resource_id_queried": resource_id}


@tool(response_format="content_and_artifact")
def initiate_resource_download_or_update(resource_ids: Union[int, List[int]], bar_ids: List[int], config: RunnableConfig, resource_origin: str = "顺网") -> Tuple[str, Dict[str, Any]]:
    """
    为一个或多个指定ID的网吧发起一个或多个资源的下载安装或更新任务
    
    Args:
        resource_ids: 需要下载的资源的唯一ID或ID列表，请先通过query_resource_presence获取
        bar_ids: 需要下载该资源的网吧ID列表，列表不能为空
        resource_origin: 游戏来源，如"顺网"、"盖伦"、"好司机"、"小蘑菇"，默认为"顺网"
    """
    try:
        # 获取用户ID和品牌ID
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        if not user_id:
            message = "未能从当前会话配置中找到有效的用户ID。"
            return message, {"error": "用户ID不存在", "resource_ids": resource_ids, "bar_ids": bar_ids}
        
        # 验证resource_origin是否有效
        valid_origins = ["顺网", "盖伦", "好司机", "小蘑菇"]
        if resource_origin not in valid_origins:
            message = f"无效的游戏来源 '{resource_origin}'，有效值为: {', '.join(valid_origins)}"
            return message, {"error": "无效的游戏来源", "resource_ids": resource_ids, "bar_ids": bar_ids}
        
        # 检查bar_ids是否为空
        if not bar_ids:
            message = "网吧ID列表不能为空。"
            return message, {"error": "网吧ID列表为空", "resource_ids": resource_ids}
        
        # 转换单个资源ID为列表
        if isinstance(resource_ids, int):
            resource_ids_list = [resource_ids]
        else:
            resource_ids_list = resource_ids
        
        # 检查resource_ids是否为空
        if not resource_ids_list:
            message = "资源ID列表不能为空。"
            return message, {"error": "资源ID列表为空", "bar_ids": bar_ids}
        
        # 调用实际的API
        from utils.api_utils import batch_download_resources, search_resources
        
        # 收集资源信息
        resource_names = {}
        invalid_resources = []
        for res_id in resource_ids_list:
            # 查询资源信息，确认资源存在
            api_result = search_resources(user_id, brand_id, resource_id=res_id, resource_origin=resource_origin)

            # 检查API调用是否成功
            if not api_result.get('success') or api_result.get('code') != 0:
                # API调用失败
                invalid_resources.append(res_id)
                error_msg = api_result.get('msg', '未知错误')
                logger.error(f"查询资源ID {res_id} 失败: {error_msg}")
                resource_names[res_id] = f"未知资源(ID: {res_id})"
            else:
                # 从API响应中获取data部分
                resource_info = api_result.get('data')
                if not resource_info or not resource_info.get("resources"):
                    # 资源不存在
                    invalid_resources.append(res_id)
                    logger.error(f"未找到ID为 {res_id} 的资源")
                    resource_names[res_id] = f"未知资源(ID: {res_id})"
                else:
                    # 使用API返回的资源信息
                    resource = resource_info["resources"][0]
                    resource_names[res_id] = resource.get("resourceName", f"未知资源(ID: {res_id})")
        
        # 如果有无效资源，返回错误信息
        if invalid_resources:
            invalid_resources_str = ", ".join(map(str, invalid_resources))
            message = f"以下资源ID不存在或查询失败: {invalid_resources_str}"
            return message, {
                "error": "资源ID不存在或查询失败", 
                "invalid_resources": invalid_resources,
                "resource_ids": resource_ids_list,
                "bar_ids": bar_ids
            }
        
        # 调用批量下载API
        api_result = batch_download_resources(user_id, resource_ids_list, bar_ids, resource_origin, brand_id)

        # 检查API调用是否成功
        is_success = api_result.get('success') and api_result.get('code') == 0

        # 准备结构化结果数据
        result_dict = {
            "resource_ids_to_download": resource_ids_list,
            "resource_names_to_download": resource_names,
            "bar_ids_to_download": bar_ids,
            "resource_origin": resource_origin,
            "api_call_success": is_success
        }

        # 如果API调用成功，返回成功消息
        if is_success:
            # 构建资源名称列表字符串
            resource_names_str = ", ".join([f"《{name}》(ID: {res_id})" for res_id, name in resource_names.items()])

            message = f"已成功为网吧ID列表 {bar_ids} 启动以下资源的下载任务，游戏来源: {resource_origin}：\n"
            message += resource_names_str + "\n"
            message += f"下载任务已提交，可稍后查询下载状态。"
            return message, result_dict
        else:
            # API调用失败，返回错误信息
            error_msg = api_result.get('msg', '未知错误')
            message = f"批量下载API调用失败：{error_msg}。无法启动下载任务。"
            result_dict["error"] = error_msg
        return message, result_dict
    
    except Exception as e:
        error_msg = f"启动资源下载任务时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "resource_ids": resource_ids, "bar_ids": bar_ids}


@tool(response_format="content_and_artifact")
def get_recent_resource_updates_info(time_period: str = "today") -> Tuple[str, Dict[str, Any]]:
    """
    查询指定时间范围内有哪些资源进行了内容更新或版本升级

    Args:
        time_period: 查询的时间段，例如 "today" (默认), "this_week", "yyyy-MM-dd"

    **重要输出要求**:
    此工具返回带有引用块标签的资源列表表格，你必须原封不动地输出，不允许删除标签或修改格式！
    """
    try:
        # 确定时间范围描述
        if time_period == "today":
            period_description = "今天"
        elif time_period == "this_week":
            period_description = "本周"
        else:
            # 尝试解析日期字符串 "yyyy-MM-dd"
            try:
                datetime.strptime(time_period, "%Y-%m-%d")
                period_description = f"{time_period}"
            except ValueError:
                # 如果解析失败，默认为今天
                period_description = "今天（默认，因为提供的时间格式无效）"
                time_period = "today"
        
        # 目前API只支持查询今日更新的游戏
        # 如果请求的不是today，返回空结果
        if time_period != "today":
            message = f"目前只支持查询今日更新的游戏，您查询的时间段【{period_description}】暂不支持。"
            return message, {"time_period_queried": time_period, "updated_resources": []}
        
        # 调用API获取今日更新的游戏
        from utils.api_utils import query_game_update_today
        
        api_result = query_game_update_today()

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"获取今日更新游戏信息失败：{error_msg}"
            return message, {"time_period_queried": time_period, "error": error_msg}

        result = api_result.get('data')
        
        # 从API结果中提取资源列表
        updated_resources = []
        resources = result.get("resources", [])
                
        for resource in resources:
                    updated_resource = {
                "resource_name": resource.get("resourceName", "未知"),
                "resource_id": resource.get("resourceId", 0),
                "resource_origin": resource.get("resourceOrigin", ""),
                "resource_size_mb": resource.get("resourceSize", 0) / 1024  # 转换为MB
                    }
                    updated_resources.append(updated_resource)
        
        # 准备结构化数据
        result_dict = {
            "time_period_queried": time_period,
            "updated_resources": updated_resources
        }
        
        # 构建带标签的表格格式消息
        if not updated_resources:
            # 没有更新资源的情况
            prefix_text = f"在指定时间段【{period_description}】内没有资源更新。"
            table_content = "| 资源ID | 游戏名称 | 来源 | 大小 |\n"
            table_content += "|---|---|---|---|\n"
            table_content += "| - | 暂无更新 | - | - |"
            message = smart_add_quote_block(prefix_text, "update-resource-list", table_content)
        else:
            # 有更新资源的情况
            prefix_text = f"在时间段【{period_description}】内更新的资源："

            # 构建资源表格内容
            table_content = "| 资源ID | 游戏名称 | 来源 | 大小 |\n"
            table_content += "|---|---|---|---|\n"

            for resource in updated_resources:
                resource_name = resource['resource_name']
                resource_id = resource['resource_id']
                resource_origin = resource.get('resource_origin', '') or ''
                resource_size_mb = resource.get('resource_size_mb', 0)
                table_content += f"| {resource_id} | {resource_name} | {resource_origin} | {resource_size_mb:.2f}MB |\n"

            # 使用智能函数添加引用块标签
            message = smart_add_quote_block(prefix_text, "update-resource-list", table_content)
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取资源更新信息时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "time_period_queried": time_period}


@tool(response_format="content_and_artifact")
def get_resource_update_status(resource_id: int, config: RunnableConfig) -> Tuple[str, Dict[str, Any]]:
    """
    查询特定资源ID在各个网吧的更新完成情况

    Args:
        resource_id: 需要查询更新状态的资源的唯一ID，请先通过query_resource_presence获取

    **重要输出要求**:
    此工具返回带有引用块标签的状态表格，你必须原封不动地输出，不允许删除标签或修改格式！
    """
    try:
        # 获取用户ID和品牌ID
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        if not user_id:
            message = "未能从当前会话配置中找到有效的用户ID。"
            return message, {"error": "用户ID不存在", "resource_id": resource_id}
        
        # 调用API获取资源信息
        from utils.api_utils import search_resources, query_bar_resource_update_result
        
        # 先查询资源信息，确认资源存在
        search_result = search_resources(user_id, brand_id, resource_id=resource_id)

        # 检查API调用是否成功
        if not search_result.get('success') or search_result.get('code') != 0:
            error_msg = search_result.get('msg', '未知错误')
            prefix_text = f"查询资源ID {resource_id} 失败：{error_msg}"
            table_content = "| 状态 | 数量 |\n|---|---|\n| 查询失败 | - |"
            message = smart_add_quote_block(prefix_text, "update", table_content)
            return message, {"resource_id_queried": resource_id, "error": error_msg}

        # 从API响应中获取data部分
        resource_info = search_result.get('data')
        if not resource_info or not resource_info.get("resources"):
            # 使用智能函数创建标准格式的表格，用于资源未找到的情况
            prefix_text = f"未找到ID为 {resource_id} 的资源。"
            table_content = "| 状态 | 数量 |\n|---|---|\n| 未找到资源 | - |"
            message = smart_add_quote_block(prefix_text, "update", table_content)
            return message, {"resource_id_queried": resource_id, "error": "资源ID不存在"}

        # 获取资源名称
        resource = resource_info["resources"][0]
        resource_name = resource.get("resourceName", f"未知资源(ID: {resource_id})")
        
        # 调用API获取网吧游戏更新结果
        api_result = query_bar_resource_update_result(user_id, resource_id, brand_id)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"获取资源《{resource_name}》(ID: {resource_id})的更新状态失败：{error_msg}"
            return message, {
                "resource_id_queried": resource_id,
                "resource_name": resource_name,
                "error": error_msg
            }

        result = api_result.get('data')
            
        # 处理API返回结果
        success_bar_num = result.get("successBarNum", 0)  # 更新完成的网吧数量
        fail_bar_num = result.get("failBarNum", 0)  # 更新失败的网吧数量
        bar_list = result.get("barList", [])  # 网吧列表
        
        # 准备结构化数据
        result_dict = {
            "resource_id_queried": resource_id,
            "resource_name": resource_name,
            "success_bar_num": success_bar_num,
            "fail_bar_num": fail_bar_num,
            "bar_list": bar_list
        }
        
        # 按照STATUS_DISPLAY_RULES构建带标签的表格格式
        prefix_text = f"资源《{resource_name}》(ID: {resource_id})的更新状态："

        # 构建状态表格内容
        table_content = "| 状态 | 数量 |\n"
        table_content += "|---|---|\n"
        table_content += f"| 更新完成网吧 | {success_bar_num} |\n"
        table_content += f"| 更新失败网吧 | {fail_bar_num} |\n"

        # 使用智能函数添加引用块标签
        message = smart_add_quote_block(prefix_text, "update", table_content)

        # 添加标准提醒和操作提示
        message += "\n*以上数据仅统计网维Online在线的网吧。*\n\n"
        message += "**您可以点击'更新失败网吧'的数量，为这些网吧重新更新该资源。**"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取资源更新状态时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "resource_id_queried": resource_id}


@tool(response_format="content_and_artifact")
def get_bars_with_failed_update(resource_id: int, config: RunnableConfig) -> Tuple[str, Dict[str, Any]]:
    """
    专门查询哪些网吧在更新指定资源ID时遇到了问题或失败了
    
    Args:
        resource_id: 需要查询的资源的唯一ID，请先通过query_resource_presence获取
    """
    try:
        # 获取用户ID和品牌ID
        user_id = config["configurable"].get("user_id")
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        if not user_id:
            message = "未能从当前会话配置中找到有效的用户ID。"
            return message, {"error": "用户ID不存在", "resource_id": resource_id}
        
        # 调用API获取资源信息
        from utils.api_utils import search_resources, query_bar_resource_update_result
        
        # 先查询资源信息，确认资源存在
        search_result = search_resources(user_id, brand_id, resource_id=resource_id)

        # 检查API调用是否成功
        if not search_result.get('success') or search_result.get('code') != 0:
            error_msg = search_result.get('msg', '未知错误')
            prefix_text = f"查询资源ID {resource_id} 失败：{error_msg}"
            table_content = "| 状态 | 数量 |\n|---|---|\n| 查询失败 | - |"
            message = smart_add_quote_block(prefix_text, "update", table_content)
            return message, {"resource_id_queried": resource_id, "error": error_msg}

        # 从API响应中获取data部分
        resource_info = search_result.get('data')
        if not resource_info or not resource_info.get("resources"):
            # 使用智能函数创建标准格式的表格，用于资源未找到的情况
            prefix_text = f"未找到ID为 {resource_id} 的资源。"
            table_content = "| 状态 | 数量 |\n|---|---|\n| 未找到资源 | - |"
            message = smart_add_quote_block(prefix_text, "update", table_content)
            return message, {"resource_id_queried": resource_id, "error": "资源ID不存在"}

        # 获取资源名称
        resource = resource_info["resources"][0]
        resource_name = resource.get("resourceName", f"未知资源(ID: {resource_id})")
            
        # 调用API获取网吧游戏更新结果
        api_result = query_bar_resource_update_result(user_id, resource_id, brand_id)

        if not api_result.get('success') or api_result.get('code') != 0:
            error_msg = api_result.get('msg', '未知错误')
            message = f"获取资源《{resource_name}》(ID: {resource_id})的更新状态失败：{error_msg}"
            return message, {
                "resource_id_queried": resource_id,
                "resource_name": resource_name,
                "error": error_msg
                }

        result = api_result.get('data')
                
        # 处理API返回结果
        fail_bar_num = result.get("failBarNum", 0)  # 更新失败的网吧数量
        bar_list = result.get("barList", [])  # 网吧列表
        
        # 假设API不直接标记哪些网吧更新失败，我们将failBarNum作为判断依据
        # 如果有失败的网吧，我们假设API返回的bar_list中包含了这些信息
        # 这里需要根据实际API返回格式调整
        
        # 准备结构化数据
        result_dict = {
            "resource_id_queried": resource_id,
            "resource_name": resource_name,
            "fail_bar_num": fail_bar_num,
            "failed_bars": bar_list if fail_bar_num > 0 else []
        }
        
        # 构建自然语言消息
        if fail_bar_num == 0:
            message = f"没有网吧在更新资源《{resource_name}》(ID: {resource_id})时失败。"
        else:
            message = f"以下{fail_bar_num}家网吧在更新资源《{resource_name}》(ID: {resource_id})时失败：\n"
            for i, bar in enumerate(bar_list, 1):
                bar_id = bar.get("barId")
                bar_name = bar.get("barName", f"未知网吧(ID: {bar_id})")
                message += f"{i}. 【{bar_name}】(ID: {bar_id})\n"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取更新失败的网吧信息时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "resource_id_queried": resource_id}
