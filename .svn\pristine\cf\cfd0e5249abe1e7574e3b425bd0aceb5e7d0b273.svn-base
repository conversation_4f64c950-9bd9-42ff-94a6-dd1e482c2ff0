# 网维小智应用配置文件
# 支持Java风格的properties格式

# API密钥配置
DASHSCOPE_API_KEY=sk-xxxx
OPENAI_API_KEY=sk-xxxxx
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1

# DeepSeek配置
# 硅基流动
DEEPSEEK_API_KEY=sk-rszrgovpckppltutjzyvvfwvzyrnmgbbpeafdyygqfntsqrl
DEEPSEEK_API_BASE=https://api.siliconflow.cn/v1

# LangSmith配置
LANGSMITH_API_KEY=lsv2_pt_xxxxx
LANGCHAIN_PROJECT=wangwei
LANGSMITH_TRACING=true

# MySQL数据库配置
MYSQL_USER=defend_develop_sec
MYSQL_PASSWORD=Swdev#445566
MYSQL_HOST=***************
MYSQL_PORT=3306
MYSQL_DB=wangwei_manage
MYSQL_POOL_SIZE=10
MYSQL_MAX_POOL_SIZE=20
MYSQL_AUTOCOMMIT=true
MYSQL_CHARSET=utf8mb4
MYSQL_HEARTBEAT_INTERVAL=60
MYSQL_POOL_RECYCLE=3600
MYSQL_PASSWORD_ENCRYPTED=false

# Redis配置
REDIS_SENTINEL_MASTER=sentinel-**************-6388
REDIS_SENTINEL_NODES=**************:6390,**************:6389,**************:6389
REDIS_DB=10
REDIS_PASSWORD=xddfgvhjk3456

# 模型配置
# 主要Agent模型 - 当前使用千问
AGENT_MODEL_NAME=qwen-turbo-latest
AGENT_MODEL_PROVIDER=tongyi
AGENT_MODEL_TEMPERATURE=0.05

# 备选模型配置（注释掉的）
# 硅基流动DeepSeek
# AGENT_MODEL_NAME=deepseek-ai/DeepSeek-V3
# AGENT_MODEL_PROVIDER=siliconflow
# DeepSeek官网
# AGENT_MODEL_NAME=deepseek-chat
# AGENT_MODEL_PROVIDER=deepseek

# 意图识别模型
INTENT_MODEL_NAME=deepseek-ai/DeepSeek-V3
INTENT_MODEL_PROVIDER=siliconflow

# 标题生成模型
TITLE_MODEL_NAME=qwen3-1.7b
TITLE_MODEL_PROVIDER=tongyi

# 网维API配置
#WANGWEI_API_BASE_URL=http://*************:9001
WANGWEI_API_BASE_URL=https://web.icafe8.com/
WANGWEI_API_MD5_KEY=sjakfnskjr

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 日志配置
LOG_DIR=logs
