"""
硬件信息工具模块
提供硬件信息查询和分析功能
"""
from typing import Dict, List, Tuple, Optional, Any, Literal
import logging
import os
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

from utils.api_utils import get_managed_bars, get_bar_assets

# 配置日志
logger = logging.getLogger(__name__)

@tool(response_format="content_and_artifact")
def get_bar_hardware_overview(config: RunnableConfig, bar_id: int = None) -> Tuple[str, Dict[str, Any]]:
    """
    提供单个网吧的硬件配置概览，包括CPU、显卡、内存和主板的主要型号及其数量分布
    
    Args:
        bar_id: 单个网吧ID
    """
    try:
        if not bar_id or not bar_id.isdigit(): 
            message = "未提供有效的网吧ID，可通过工具get_managed_bars_by_user查询用户管理的网吧列表让用户选择"
            return message, {"error": "无效的网吧ID"}
        
        # 确定要查询的网吧列表
        user_id = config["configurable"]["user_id"]
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        
        # 调用封装的API获取网吧资产项统计信息
        api_data = get_bar_assets(user_id, bar_id, brand_id)
        
        if api_data is None:
            message = "获取网吧硬件概览失败，请稍后再试。"
            return message, {"error": "API调用失败"}
        elif api_data == []:
            message = "网吧无硬件信息，请稍后再试。"
            return message, {"error": "网吧无硬件信息"}
        
        # 处理API返回的数据
        overview_by_bar = []
        
        bar_id = api_data.get('barId')
        bar_name = api_data.get('barName')
        asset_items = api_data.get('assetItems', [])
        
        # 分类处理不同类型的硬件
        cpu_distribution = []
        gpu_distribution = []
        ram_distribution = []
        motherboard_distribution = []
        
        for item in asset_items:
            item_type = item.get('type')
            details = item.get('details', [])
            
            if item_type == 0:  # CPU
                for detail in details:
                    cpu_distribution.append({
                        'model': detail.get('name', '未知'),
                        'brand': detail.get('brand', '未知'),
                        'count': detail.get('count', 0)
                    })
            elif item_type == 2:  # GPU
                for detail in details:
                    gpu_distribution.append({
                        'model': detail.get('name', '未知'),
                        'brand': detail.get('brand', '未知'),
                        'count': detail.get('count', 0)
                    })
            elif item_type == 1:  # 内存
                for detail in details:
                    ram_distribution.append({
                        'size_gb': detail.get('capacity', 0),
                        'type': detail.get('smbiosMemoryTypeDesc', '未知'),
                        'frequency_mhz': detail.get('speed', 0),
                        'total_sticks': detail.get('count', 0)
                    })
            elif item_type == 3:  # 主板
                for detail in details:
                    motherboard_distribution.append({
                        'model': detail.get('model', '未知'),
                        'brand': detail.get('brand', '未知'),
                        'count': detail.get('count', 0)
                    })
        
        # 组装概览信息
        bar_overview = {
            "bar_id": bar_id,
            "bar_name": bar_name,
            "cpu_distribution": cpu_distribution,
            "gpu_distribution": gpu_distribution,
            "ram_distribution": ram_distribution,
            "motherboard_distribution": motherboard_distribution
        }
        
        overview_by_bar.append(bar_overview)
        
        # 准备结构化数据
        result_dict = {
            "query_scope": {"bar_ids_queried": bar_id, "description": "指定网吧"},
            "overview_by_bar": overview_by_bar
        }
        
        # 构建自然语言消息
        message = ""
        for bar_overview in overview_by_bar:
            message += f"网吧【{bar_overview['bar_name']}】(ID: {bar_overview['bar_id']})的硬件概览如下："
            
            # CPU信息
            if bar_overview['cpu_distribution']:
                message += "CPU主要有"
                for i, cpu in enumerate(bar_overview['cpu_distribution']):
                    if i > 0:
                        message += "，"
                    message += f"【{cpu['model']}】{cpu['count']}台"
            
            # 显卡信息
            if bar_overview['gpu_distribution']:
                message += "；显卡主要有"
                for i, gpu in enumerate(bar_overview['gpu_distribution']):
                    if i > 0:
                        message += "，"
                    message += f"【{gpu['model']}】{gpu['count']}台"
            
            # 内存信息
            if bar_overview['ram_distribution']:
                message += "；内存主要为"
                for i, ram in enumerate(bar_overview['ram_distribution']):
                    if i > 0:
                        message += "，"
                    message += f"【{ram['size_gb']}GB {ram['type']}】{ram['total_sticks']}条"
            
            # 主板信息
            if bar_overview['motherboard_distribution']:
                message += "；主板主要为"
                for i, mb in enumerate(bar_overview['motherboard_distribution']):
                    if i > 0:
                        message += "，"
                    message += f"【{mb['model']}】{mb['count']}块"
            
            message += "。\n"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取网吧硬件概览时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e)}


@tool(response_format="content_and_artifact")
def get_component_details_in_bars(
    component_type: Literal["cpu", "gpu", "ram", "motherboard"], 
    config: RunnableConfig,
    bar_ids: Optional[List[int]] = None
) -> Tuple[str, Dict[str, Any]]:
    """
    获取指定硬件组件类型（CPU、显卡、内存、主板）在特定网吧（或用户管理的所有网吧）中的详细型号、规格和数量分布
    
    Args:
        component_type: 要查询的硬件组件类型，必须是 "cpu", "gpu", "ram", "motherboard" 之一
        bar_ids: 网吧ID列表。如果未提供或为空，则默认为当前用户管理的所有网吧
    """
    try:
        # 验证组件类型
        if component_type not in ["cpu", "gpu", "ram", "motherboard"]:
            message = f"无效的组件类型: {component_type}。必须是 'cpu', 'gpu', 'ram' 或 'motherboard'。"
            return message, {"error": "无效的组件类型", "component_type_queried": component_type}
        
        # 组件类型映射到API参数
        component_type_map = {
            "cpu": 0,
            "gpu": 2,
            "ram": 1,
            "motherboard": 3
        }
        
        # 确定要查询的网吧列表
        user_id = config["configurable"]["user_id"]
        brand_id = config["configurable"].get("brand_id", 1)  # 默认品牌ID为1
        
        # 如果没有提供bar_ids，则获取用户管理的所有网吧
        if not bar_ids:
            # 调用封装的API获取用户管理的所有网吧
            bars_data = get_managed_bars(user_id, brand_id)
            
            if not bars_data:
                message = "获取用户管理的网吧信息失败，请稍后再试。"
                return message, {"error": "API调用失败", "component_type_queried": component_type}
            
            target_bars = [bar.get('barId') for bar in bars_data]
            scope_description = {"description": "用户管理的所有网吧"}
        else:
            # 如果提供了bar_ids，则使用提供的列表
            # 调用封装的API获取用户管理的所有网吧进行权限验证
            bars_data = get_managed_bars(user_id, brand_id)
            
            if not bars_data:
                message = "获取用户管理的网吧信息失败，请稍后再试。"
                return message, {"error": "API调用失败", "component_type_queried": component_type}
            
            managed_bar_ids = [bar.get('barId') for bar in bars_data]
            bar_ids = list(set(bar_ids).intersection(set(managed_bar_ids)))
            target_bars = bar_ids
            scope_description = {"bar_ids_queried": target_bars, "description": "指定网吧"}
        
        # 调用封装的API获取网吧资产项统计信息
        api_data = get_bar_assets(user_id, target_bars, brand_id, component_type_map[component_type])
        
        if not api_data:
            message = f"获取{component_type}详情失败，请稍后再试。"
            return message, {"error": "API调用失败", "component_type_queried": component_type}
        
        # 处理API返回的数据
        distribution_by_bar = []
        
        for bar_data in api_data:
            bar_id = bar_data.get('barId')
            bar_name = bar_data.get('barName')
            asset_items = bar_data.get('assetItems', [])
            
            # 筛选指定类型的组件
            component_details = []
            
            for item in asset_items:
                item_type = item.get('type')
                if item_type != component_type_map[component_type]:
                    continue
                
                count = item.get('count', 0)
                
                if component_type == "cpu":
                    component_details.append({
                        'brand': item.get('brand', '未知'),
                        'model': item.get('name', '未知'),
                        'family': item.get('family', '未知'),
                        'series': item.get('series', '未知'),
                        'client_machine_count': count
                    })
                elif component_type == "gpu":
                    component_details.append({
                        'brand': item.get('brand', '未知'),
                        'model': item.get('name', '未知'),
                        'family': item.get('family', '未知'),
                        'series': item.get('series', '未知'),
                        'client_machine_count': count
                    })
                elif component_type == "ram":
                    component_details.append({
                        'size_gb': item.get('capacity', 0),
                        'type': item.get('smbiosMemoryTypeName', '未知'),
                        'frequency_mhz': item.get('configuredClockSpeed', 0),
                        'client_machine_count': count
                    })
                elif component_type == "motherboard":
                    component_details.append({
                        'brand': item.get('brand', '未知'),
                        'model': item.get('model', '未知'),
                        'client_machine_count': count
                    })
            
            distribution_by_bar.append({
                "bar_id": bar_id,
                "bar_name": bar_name,
                "component_details": component_details
            })
        
        # 准备结构化数据
        result_dict = {
            "component_type_queried": component_type,
            "query_scope": scope_description,
            "distribution_by_bar": distribution_by_bar
        }
        
        # 构建自然语言消息
        message = ""
        for bar_distribution in distribution_by_bar:
            message += f"网吧【{bar_distribution['bar_name']}】(ID: {bar_distribution['bar_id']})的{component_type.upper()}分布情况："
            
            if not bar_distribution['component_details']:
                message += "无数据。\n"
                continue
            
            for i, component in enumerate(bar_distribution['component_details']):
                if i > 0:
                    message += "；"
                
                if component_type == "cpu":
                    message += f"【{component['brand']} {component['model']}】有{component['client_machine_count']}台客户机"
                elif component_type == "gpu":
                    message += f"【{component['brand']} {component['model']}】有{component['client_machine_count']}台客户机"
                elif component_type == "ram":
                    message += f"【{component['size_gb']}GB {component['type']} {component.get('frequency_mhz', '')}MHz】有{component['client_machine_count']}台客户机"
                elif component_type == "motherboard":
                    message += f"【{component['brand']} {component['model']}】有{component['client_machine_count']}台客户机"
            
            message += "。\n"
        
        return message, result_dict
    
    except Exception as e:
        error_msg = f"获取组件详情时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "component_type_queried": component_type}


# @tool(response_format="content_and_artifact")
def count_machines_by_component_filter_in_bars(
    component_type: Literal["cpu", "gpu", "ram", "motherboard"],
    filters: Dict[str, Any],
    config: RunnableConfig,
    bar_ids: Optional[List[int]] = None
) -> Tuple[str, Dict[str, Any]]:
    """
    根据指定的硬件组件及其具体属性，统计在特定网吧（或用户管理的所有网吧）中满足条件的客户机数量
    
    Args:
        component_type: 组件类型，必须是 "cpu", "gpu", "ram", "motherboard" 之一
        filters: 过滤条件，字段名需与后台数据匹配。例如CPU: {"brand": "Intel", "model": "i5-9400F"}
        bar_ids: 网吧ID列表。如果未提供或为空，则默认为当前用户管理的所有网吧
    """
    try:
        # 验证组件类型
        if component_type not in ["cpu", "gpu", "ram", "motherboard"]:
            message = f"无效的组件类型: {component_type}。必须是 'cpu', 'gpu', 'ram' 或 'motherboard'。"
            return message, {"error": "无效的组件类型", "component_type_queried": component_type}
        
        # 目前API暂不支持按组件过滤查询机器数量的功能
        message = "抱歉，当前API暂不支持按组件过滤查询机器数量的功能。"
        return message, {
            "error": "API不支持此功能", 
            "component_type_queried": component_type,
            "filters": filters
        }
    
    except Exception as e:
        error_msg = f"按组件过滤客户机时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "component_type_queried": component_type, "filters": filters}


# @tool(response_format="content_and_artifact")
def find_machines_by_criteria_in_bars(
    criteria: Dict[str, Dict[str, Any]],
    config: RunnableConfig,
    bar_ids: Optional[List[int]] = None
) -> Tuple[str, Dict[str, Any]]:
    """
    根据多种硬件组件的组合条件，查找特定网吧（或用户管理的所有网吧）中符合这些条件的客户机
    
    Args:
        criteria: 多种硬件组件的组合条件，格式为{组件类型: {属性: 值}}，例如{"cpu": {"brand": "Intel"}, "gpu": {"brand": "NVIDIA"}}
        bar_ids: 网吧ID列表。如果未提供或为空，则默认为当前用户管理的所有网吧
    """
    try:
        # 验证条件格式
        valid_component_types = ["cpu", "gpu", "ram", "motherboard"]
        for component_type in criteria.keys():
            if component_type not in valid_component_types:
                message = f"无效的组件类型: {component_type}。必须是 {', '.join(valid_component_types)} 之一。"
                return message, {"error": "无效的组件类型", "criteria": criteria}
        
        # 目前API暂不支持按多条件查找客户机的功能
        message = "抱歉，当前API暂不支持按多条件查找客户机的功能。"
        return message, {
            "error": "API不支持此功能", 
            "criteria": criteria
        }
    
    except Exception as e:
        error_msg = f"按多条件查找客户机时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "criteria": criteria}


# @tool(response_format="content_and_artifact")
def find_bars_by_hardware_profile(
    profile_criteria: Dict[str, Any],
    config: RunnableConfig,
    min_matching_machines_in_bar: int = 1
) -> Tuple[str, Dict[str, Any]]:
    """
    根据硬件配置条件，查找拥有符合要求的客户机数量超过指定阈值的网吧
    
    Args:
        profile_criteria: 硬件配置条件，格式为{组件类型: {属性: 值}}，例如{"cpu": {"brand": "Intel"}}
        min_matching_machines_in_bar: 最小符合条件的客户机数量，默认为1，表示至少有1台客户机符合条件
    """
    try:
        # 验证条件格式
        valid_component_types = ["cpu", "gpu", "ram", "motherboard"]
        for component_type in profile_criteria.keys():
            if component_type not in valid_component_types:
                message = f"无效的组件类型: {component_type}。必须是 {', '.join(valid_component_types)} 之一。"
                return message, {"error": "无效的组件类型", "profile_criteria": profile_criteria}
        
        # 目前API暂不支持按硬件配置查找网吧的功能
        message = "抱歉，当前API暂不支持按硬件配置查找网吧的功能。"
        return message, {
            "error": "API不支持此功能", 
            "profile_criteria": profile_criteria,
            "min_matching_machines_required": min_matching_machines_in_bar
        }
    
    except Exception as e:
        error_msg = f"按硬件配置查找网吧时发生错误: {str(e)}"
        logger.error(error_msg)
        return error_msg, {"error": str(e), "profile_criteria": profile_criteria}